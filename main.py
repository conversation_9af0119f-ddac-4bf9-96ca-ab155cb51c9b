import os
import requests
import json
import string
import time
import logging
import re
from docx import Document
from datetime import datetime, timedelta
from pathlib import Path
import subprocess
from tqdm import tqdm
import whisper
import torch
import shutil
import tarfile
import io
from pydub import AudioSegment
import azure.cognitiveservices.speech as speechsdk


class TTSGenerator:
    def convert_json_to_srt(self, subtitle_data, output_file):
        """将MiniMax返回的JSON字幕转换为SRT格式"""
        try:
            # 检查字幕数据格式
            if not isinstance(subtitle_data, list):
                self.log_message("字幕数据格式不正确，应为列表", "ERROR")
                return False
            
            # 记录字幕数据的第一项，用于调试
            if subtitle_data:
                self.log_message(f"字幕数据第一项: {json.dumps(subtitle_data[0], ensure_ascii=False)}")
            
            srt_content = []
            for i, item in enumerate(subtitle_data, 1):
                # 检查各种可能的字段名称
                # 开始时间字段可能的名称
                start_time_field = None
                for field in ['start_time', 'begin_time', 'start', 'begin', 'startTime', 'beginTime', 'time_begin']:
                    if field in item:
                        start_time_field = field
                        break
                
                # 结束时间字段可能的名称
                end_time_field = None
                for field in ['end_time', 'finish_time', 'end', 'finish', 'endTime', 'finishTime', 'time_end']:
                    if field in item:
                        end_time_field = field
                        break
                
                # 文本内容字段可能的名称
                text_field = None
                for field in ['text', 'content', 'subtitle', 'words', 'sentence']:
                    if field in item:
                        text_field = field
                        break
                
                # 如果缺少任何必要字段，则跳过此条目
                if not start_time_field or not end_time_field or not text_field:
                    self.log_message(f"跳过字幕条目，缺少必要字段: {json.dumps(item, ensure_ascii=False)}", "WARNING")
                    continue
                
                # 获取开始和结束时间（毫秒）
                start_ms = item[start_time_field]
                end_ms = item[end_time_field]
                text = item[text_field]
                
                # 检查时间格式是否为数字（毫秒）
                if not isinstance(start_ms, (int, float)):
                    self.log_message(f"开始时间不是数字: {start_ms}", "WARNING")
                    # 尝试将字符串转换为数字
                    try:
                        start_ms = float(start_ms)
                    except:
                        continue
                
                if not isinstance(end_ms, (int, float)):
                    self.log_message(f"结束时间不是数字: {end_ms}", "WARNING")
                    # 尝试将字符串转换为数字
                    try:
                        end_ms = float(end_ms)
                    except:
                        continue
                
                # 转换为SRT时间格式 (HH:MM:SS,mmm)
                start_time = self.ms_to_srt_time(start_ms)
                end_time = self.ms_to_srt_time(end_ms)
                
                # 添加SRT条目
                srt_entry = f"{i}\n{start_time} --> {end_time}\n{text}\n"
                srt_content.append(srt_entry)
            
            # 写入SRT文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(srt_content))
            
            # 记录生成的SRT内容长度
            self.log_message(f"生成的SRT文件包含 {len(srt_content)} 条字幕")
                
            return True
        except Exception as e:
            self.log_message(f"转换字幕格式失败: {str(e)}", "ERROR")
            return False
        
    def ms_to_srt_time(self, milliseconds):
        """将毫秒转换为SRT时间格式 (HH:MM:SS,mmm)"""
        # 确保毫秒是整数
        milliseconds = float(milliseconds)  # 先转换为浮点数以确保兼容性
        
        seconds, ms = divmod(milliseconds, 1000)
        minutes, seconds = divmod(int(seconds), 60)
        hours, minutes = divmod(int(minutes), 60)
        
        # 确保 ms 是整数
        ms = int(ms)
        
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d},{ms:03d}"
    
    def srt_time_to_ms(self, time_str):
        """将SRT时间格式转换为毫秒
        
        Args:
            time_str: SRT时间格式字符串，例如 "00:00:01,500"
            
        Returns:
            毫秒数
        """
        try:
            # 分离时、分、秒和毫秒
            main_part, ms_part = time_str.split(',')
            h, m, s = main_part.split(':')
            
            # 转换为毫秒
            total_ms = int(h) * 3600000 + int(m) * 60000 + int(s) * 1000 + int(ms_part)
            return total_ms
        except Exception as e:
            self.log_message(f"SRT时间格式转换失败: {str(e)}", "ERROR")
            return 0

    def split_subtitle_text(self, text):
        """将字幕文本按照要求切割，每句话最长不超过13个字
        如果第13个字是中文字符，则向后寻找第一个标点符号，在符号处切割"""
        if not text or len(text) <= 13:
            return [text]
        
        # 中文标点符号列表
        punctuations = ['，', '。', '、', '；', '：', '？', '！', '…', '"', '"', "'", "'", '）', '】', '》', '」', '』', '〕', '｝', '］']
        # 添加英文标点
        punctuations.extend([',', '.', ';', ':', '?', '!', ')', ']', '}'])
        
        result = []
        start = 0
        
        while start < len(text):
            # 如果剩余文本长度小于等于13，直接添加并结束
            if len(text) - start <= 13:
                result.append(text[start:])
                break
            
            # 初始切割点设为start+13
            cut_point = start + 13
            
            # 检查第13个字符是否是中文
            is_chinese = '\u4e00' <= text[cut_point] <= '\u9fff'
            
            if is_chinese:
                # 向后查找最近的标点符号
                found_punct = False
                for i in range(cut_point, min(cut_point + 10, len(text))):
                    if text[i] in punctuations:
                        # 在标点符号后切割
                        cut_point = i + 1
                        found_punct = True
                        break
                
                # 如果没找到标点符号但已经接近文本末尾，直接取剩余文本
                if not found_punct and len(text) - cut_point < 5:
                    cut_point = len(text)
            
            # 添加切割后的文本
            result.append(text[start:cut_point])
            start = cut_point
        
        return result

    def optimize_srt_file(self, srt_file_path):
        """优化SRT文件，将长字幕切割成短字幕"""
        try:
            self.log_message(f"开始优化SRT文件: {srt_file_path}")
            
            # 读取原始SRT文件
            with open(srt_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析SRT内容
            srt_blocks = content.strip().split('\n\n')
            new_blocks = []
            new_index = 1
            
            for block in srt_blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    self.log_message(f"跳过无效的SRT块: {block}", "WARNING")
                    continue
                
                # 提取时间轴和文本
                try:
                    index = int(lines[0])
                    time_line = lines[1]
                    text = '\n'.join(lines[2:])
                    
                    # 解析时间
                    time_parts = time_line.split(' --> ')
                    if len(time_parts) != 2:
                        self.log_message(f"无效的时间轴格式: {time_line}", "WARNING")
                        new_blocks.append(block)
                        continue
                    
                    start_time, end_time = time_parts
                    
                    # 切割字幕文本
                    split_texts = self.split_subtitle_text(text)
                    
                    if len(split_texts) == 1:
                        # 如果没有切割，保持原样
                        new_blocks.append(f"{new_index}\n{time_line}\n{text}")
                        new_index += 1
                    else:
                        # 计算每段字幕的时间
                        start_ms = self.srt_time_to_ms(start_time)
                        end_ms = self.srt_time_to_ms(end_time)
                        total_duration = end_ms - start_ms
                        segment_duration = total_duration / len(split_texts)
                        
                        # 为每个切割后的文本创建新的字幕块
                        for i, segment_text in enumerate(split_texts):
                            segment_start_ms = start_ms + i * segment_duration
                            segment_end_ms = segment_start_ms + segment_duration
                            
                            segment_start_time = self.ms_to_srt_time(segment_start_ms)
                            segment_end_time = self.ms_to_srt_time(segment_end_ms)
                            
                            new_blocks.append(f"{new_index}\n{segment_start_time} --> {segment_end_time}\n{segment_text}")
                            new_index += 1
                            
                except Exception as e:
                    self.log_message(f"处理SRT块时出错: {str(e)}", "ERROR")
                    # 保留原始块
                    new_blocks.append(block)
            
            # 写入优化后的SRT文件
            optimized_content = '\n\n'.join(new_blocks)
            with open(srt_file_path, 'w', encoding='utf-8') as f:
                f.write(optimized_content)
            
            self.log_message(f"SRT文件优化完成: {srt_file_path}")
            return True
            
        except Exception as e:
            self.log_message(f"优化SRT文件失败: {str(e)}", "ERROR")
            return False
    
    def __init__(self):
        # 设置Azure SDK的日志级别为最高级别以禁用所有日志
        logging.getLogger('azure').setLevel(logging.CRITICAL)
        logging.getLogger('websockets').setLevel(logging.CRITICAL)
        
        # 基础配置
        self.desktop_path = str(Path.home() / "Desktop")
        # 更新路径以匹配新的文件夹结构
        self.rewrite_path = os.path.join(self.desktop_path, "Youtube/改写")
        
        # MiniMax API配置
        self.minimax_api_key = ""
        self.minimax_group_id = ""
        
        # Azure TTS配置
        self.azure_subscription_key = ""
        self.azure_region = ""
        self.azure_voice_name = ""
        
        # Whisper模型配置
        self.whisper_model = "small"  # 默认使用small模型，平衡速度和准确度
        
        # 初始化Azure语音配置
        try:
            self.speech_config = speechsdk.SpeechConfig(
                subscription=self.azure_subscription_key, 
                region=self.azure_region
            )
            self.speech_config.set_speech_synthesis_output_format(
                speechsdk.SpeechSynthesisOutputFormat.Audio48Khz192KBitRateMonoMp3
            )
            self.speech_config.speech_synthesis_voice_name = self.azure_voice_name
        except Exception as e:
            self.log_message(f"初始化Azure TTS失败: {str(e)}", "ERROR")
            self.speech_config = None
        
        # 初始化文件选择和模式
        self.selected_files = []
        self.file_modes = {}
        self.current_mode = None
        self.file_mapping = {}  # 新增：文件映射初始化
        
        # 选择Whisper模型
        self.select_whisper_model()
        
        # 开始处理文件
        self.select_and_process_files()

    def select_whisper_model(self):
        """选择Whisper模型大小"""
        print("\n=== 选择Whisper语音识别模型 ===")
        print("可用的模型大小:")
        print("1. tiny   - 最小的模型，速度最快但准确度最低")
        print("2. base   - 小型模型，速度较快，准确度适中")
        print("3. small  - 中型模型，平衡了速度和准确度（默认）")
        print("4. medium - 较大模型，准确度高，速度较慢")
        print("5. large  - 最大的模型，准确度最高，但速度最慢")
        
        while True:
            choice = input("\n请选择模型大小 (1-5，默认3): ").strip()
            
            if not choice:
                self.whisper_model = "small"  # 默认
                break
                
            try:
                choice_num = int(choice)
                if 1 <= choice_num <= 5:
                    models = ["tiny", "base", "small", "medium", "large"]
                    self.whisper_model = models[choice_num - 1]
                    break
                else:
                    print("请输入1到5之间的数字")
            except ValueError:
                print("请输入有效的数字")
        
        self.log_message(f"已选择Whisper模型: {self.whisper_model}")
    
    def select_and_process_files(self):
        """选择并处理文件"""
        # 获取可用文件列表
        docx_files = self.get_docx_files()
        if not docx_files:
            self.log_message("没有找到可用文件", "ERROR")
            return
            
        # 显示文件列表
        self.log_message("\n可用文件:")
        for i, file in enumerate(docx_files, 1):
            self.log_message(f"{i}. {file}")
        
        # 文件选择循环
        self.log_message("\n请选择文件编号（多个文件用空格分隔）: ", "INFO")
        file_input = input().strip()
            
        try:
            # 解析输入的文件编号
            indices = [int(idx) - 1 for idx in file_input.split()]
            # 检查每个编号的有效性
            valid_indices = [idx for idx in indices if 0 <= idx < len(docx_files)]
            
            # 显示无效的选择
            invalid_indices = [idx + 1 for idx in indices if idx not in valid_indices]
            if invalid_indices:
                self.log_message(f"无效的文件编号: {', '.join(map(str, invalid_indices))}", "ERROR")
                return
            
            # 获取要处理的文件
            files_to_process = [docx_files[idx] for idx in valid_indices]
            if not files_to_process:
                self.log_message("未选择任何有效文件", "ERROR")
                return
            
            # 为每个文件选择模式
            for file_name in files_to_process:
                self.log_message(f"\n为文件 {file_name} 选择模式:")
                self.select_mode(file_name)
                self.selected_files.append(file_name)
            
            # 开始处理文件
            self.log_message("\n开始处理选中的文件...")
            self.process_files()  # 只调用一次
            
        except ValueError:
            self.log_message("输入无效，请输入数字编号", "ERROR")

    def display_available_files(self, docx_files):
        """显示可用文件列表，标记已选择的文件"""
        self.log_message("\n可用文件列表 (已选择的文件已标记):")
        for i, file in enumerate(docx_files, 1):
            status = " [已选择]" if file in self.selected_files else ""
            self.log_message(f"{i}. {file}{status}")

    def select_mode(self, file_name):
        """为文件选择处理模式"""
        while True:
            if self.speech_config is None:
                self.log_message("1: 通用声音\n2: 奇幻声音\n请选择(1-2): ", "INFO")
                mode_input = input().strip()
                try:
                    mode = int(mode_input)
                    if 1 <= mode <= 2:
                        self.file_modes[file_name] = mode
                        break
                except ValueError:
                    pass
            else:
                self.log_message("1: 通用声音\n2: 奇幻声音\n3: Azure TTS\n4: 墨西哥西班牙语 (Jorge)\n请选择(1-4): ", "INFO")
                mode_input = input().strip()
                try:
                    mode = int(mode_input)
                    if 1 <= mode <= 4:
                        self.file_modes[file_name] = mode
                        break
                except ValueError:
                    pass
            self.log_message("请输入有效的数字")

    def split_text(self, text):
        """按照标点符号分割文本，保持每段的完整性"""
        # 去除多余的空白字符
        text = ' '.join(text.split())
        
        # 初始化结果列表
        segments = []
        current_segment = ""
        
        # 遍历文本
        for char in text:
            current_segment += char
            # 当遇到分隔符时，保存当前段落
            if char in ['.', ',', '。', '，']:
                # 去掉末尾的标点
                clean_segment = current_segment[:-1].strip()
                if clean_segment:
                    segments.append(clean_segment)
                current_segment = ""
        
        # 处理最后一段
        if current_segment.strip():
            segments.append(current_segment.strip())
        
        return [s for s in segments if s]

    def generate_srt(self, segments, start_time=0):
        """生成标准格式的SRT文件内容"""
        srt_content = []
        current_time = start_time
        
        for i, segment in enumerate(segments, 1):
            # 计算持续时间（基于文本长度，每个字约0.3秒）
            duration = len(segment) * 0.3
            duration = max(1.5, min(duration, 5))  # 最短1.5秒，最长5秒
            
            # 格式化时间戳
            start = self.format_timestamp(current_time)
            end = self.format_timestamp(current_time + duration)
            
            # 添加SRT条目（确保每个部分都有换行）
            srt_content.append(f"{i}\n{start} --> {end}\n{segment}\n")
            
            # 更新下一段的开始时间（添加0.1秒间隔）
            current_time += duration + 0.1
        
        return "\n".join(srt_content)

    def format_timestamp(self, seconds):
        """将秒数转换为 SRT 格式的时间戳"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        msecs = int((seconds * 1000) % 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{msecs:03d}"

    def get_current_time(self):
        """获取当前时间的格式化字符串"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
    def log_message(self, message, level="INFO"):
        """输出日志信息"""
        print(f"[{self.get_current_time()}] [{level}] {message}")
            
    def read_docx(self, display_name):
        """从改写文件夹读取指定的文件内容"""
        try:
            # 从映射中获取原始文件名
            original_file = self.file_mapping.get(display_name)
            if not original_file:
                self.log_message(f"找不到文件映射: {display_name}", "ERROR")
                return None
                
            file_path = os.path.join(self.rewrite_path, original_file)
            if not os.path.exists(file_path):
                self.log_message(f"文件不存在: {file_path}", "ERROR")
                return None
            
            # 根据文件类型读取内容
            if file_path.endswith('.docx'):
                doc = Document(file_path)
                text = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():  # 只添加非空段落
                        text.append(paragraph.text.strip())
                return '\n'.join(text)
            elif file_path.endswith('.txt'):
                # 读取文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            else:
                self.log_message(f"不支持的文件格式: {file_path}", "ERROR")
                return None
            
        except Exception as e:
            self.log_message(f"读取文件失败: {str(e)}", "ERROR")
            return None

    def download_file(self, file_id, output_file):
        """下载文件"""
        try:
            # 先获取文件信息
            retrieve_url = f"https://api.minimax.chat/v1/files/retrieve?GroupId={self.minimax_group_id}&file_id={file_id}"
            headers = {
                "Authorization": f"Bearer {self.minimax_api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(retrieve_url, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if "base_resp" not in data or data["base_resp"]["status_code"] != 0:
                error_msg = data.get("base_resp", {}).get("status_msg", "未知错误")
                self.log_message(f"获取文件信息失败: {error_msg}", "ERROR")
                return False
            
            # 获取下载链接
            file_info = data.get("file", {})
            download_url = file_info.get("download_url")
            if not download_url:
                self.log_message("未能获取下载链接", "ERROR")
                return False
            
            # 下载文件内容
            download_response = requests.get(
                f"https://api.minimax.chat/v1/files/retrieve_content?GroupId={self.minimax_group_id}&file_id={file_id}",
                headers=headers
            )
            download_response.raise_for_status()
            
            # 保存文件
            with open(output_file, "wb") as f:
                f.write(download_response.content)
            
            self.log_message(f"文件已保存到: {output_file}")
            return True
            
        except Exception as e:
            self.log_message(f"下载文件失败: {str(e)}", "ERROR")
            return False

    def call_minimax_api(self, text, output_file, mode="normal", generate_srt=True):
        """使用MiniMax API生成语音"""
        self.log_message("正在创建MiniMax语音合成任务...")
        
        # 使用T2A V2 API，支持字幕生成
        base_url = "https://api.minimax.chat/v1/t2a_v2"
        headers = {
            "Authorization": f"Bearer {self.minimax_api_key}",
            "Content-Type": "application/json",
        }
        
        voice_id = "audiobook_male_1" if mode == "normal" else "audiobook_male_2"
        
        payload = {
            "model": "speech-01-turbo",
            "text": text,
            "stream": False,
            "subtitle_enable": generate_srt,  # 启用字幕功能
            "voice_setting": {
                "voice_id": voice_id,
                "speed": 1,
                "vol": 1,
                "pitch": 0
            },
            "audio_setting": {
                "sample_rate": 32000,
                "bitrate": 128000,
                "format": "mp3",
                "channel": 2
            }
        }
        
        try:
            # 直接调用T2A V2 API（同步方式）
            url = f"{base_url}?GroupId={self.minimax_group_id}"
            response = requests.post(url, headers=headers, json=payload)
            response.raise_for_status()
            result = response.json()
            
            if "base_resp" not in result or result["base_resp"]["status_code"] != 0:
                error_msg = result.get("base_resp", {}).get("status_msg", "未知错误")
                self.log_message(f"API请求失败: {error_msg}", "ERROR")
                return False
            
            # 检查是否有音频数据
            if "data" not in result or "audio" not in result["data"]:
                self.log_message("API返回中没有音频数据", "ERROR")
                return False
                
            # 获取音频数据（hex格式）
            audio_hex = result["data"]["audio"]
            audio_data = bytes.fromhex(audio_hex)
            
            # 保存音频文件
            with open(output_file, "wb") as f:
                f.write(audio_data)
            
            self.log_message(f"音频文件已保存到: {output_file}")
            
            # 处理字幕文件（如果有）
            if generate_srt and "subtitle_file" in result:
                subtitle_url = result["subtitle_file"]
                self.log_message(f"检测到字幕文件链接: {subtitle_url}")
                
                # 下载字幕文件（JSON格式）
                try:
                    subtitle_response = requests.get(subtitle_url)
                    subtitle_response.raise_for_status()
                    subtitle_data = subtitle_response.json()
                    
                    # 输出字幕数据结构信息便于调试
                    self.log_message(f"字幕数据类型: {type(subtitle_data)}")
                    if isinstance(subtitle_data, list):
                        self.log_message(f"字幕条目数量: {len(subtitle_data)}")
                        if len(subtitle_data) > 0:
                            self.log_message(f"第一条字幕数据结构: {subtitle_data[0].keys() if isinstance(subtitle_data[0], dict) else '非字典格式'}")
                    
                    # 保存原始JSON字幕文件
                    json_subtitle_file = output_file.rsplit(".", 1)[0] + ".json"
                    with open(json_subtitle_file, "w", encoding="utf-8") as f:
                        json.dump(subtitle_data, f, ensure_ascii=False, indent=2)
                    self.log_message(f"JSON字幕文件已保存: {json_subtitle_file}")
                    
                    # 转换为SRT格式并保存
                    srt_subtitle_file = output_file.rsplit(".", 1)[0] + ".srt"
                    self.log_message(f"开始转换为SRT格式，目标文件: {srt_subtitle_file}")
                    success = self.convert_json_to_srt(subtitle_data, srt_subtitle_file)
                    
                    if success:
                        self.log_message(f"SRT字幕文件已成功保存: {srt_subtitle_file}")
                        # 优化SRT字幕文件
                        self.optimize_srt_file(srt_subtitle_file)
                    else:
                        self.log_message(f"SRT字幕文件转换失败", "ERROR")
                except Exception as e:
                    self.log_message(f"下载或处理字幕文件失败: {str(e)}", "ERROR")
            else:
                self.log_message("同步API没有返回字幕文件，尝试使用异步API生成字幕", "INFO")
                # 如果同步API没有返回字幕，尝试使用异步API生成字幕
                self.call_minimax_async_api(text, output_file, mode)
            
            return True
                
        except requests.exceptions.RequestException as e:
            self.log_message(f"API请求失败: {str(e)}", "ERROR")
            return False
        except Exception as e:
            self.log_message(f"调用MiniMax API失败: {str(e)}", "ERROR")
            return False
            
    def call_minimax_async_api(self, text, output_file, mode="normal"):
        """使用MiniMax异步API生成语音和字幕"""
        self.log_message("正在创建MiniMax异步语音合成任务...")
        
        # 创建临时文本文件
        temp_dir = os.path.join(self.desktop_path, "Youtube/temp")
        os.makedirs(temp_dir, exist_ok=True)
        # 使用只包含字母和数字的文件名
        temp_txt_file = os.path.join(temp_dir, "temptext.txt")
        temp_zip_file = os.path.join(temp_dir, "temptext.zip")
        
        # 保存文本到临时文件
        with open(temp_txt_file, 'w', encoding='utf-8') as f:
            f.write(text)
        
        # 创建ZIP文件
        import zipfile
        with zipfile.ZipFile(temp_zip_file, 'w') as zipf:
            # 确保ZIP内的文件名也只包含字母和数字
            zipf.write(temp_txt_file, "temptext.txt")
        
        # 设置API参数
        url = f"https://api.minimax.chat/v1/t2a_async?GroupId={self.minimax_group_id}"
        headers = {
            "Authorization": f"Bearer {self.minimax_api_key}"
        }
        
        voice_id = "audiobook_male_1" if mode == "normal" else "audiobook_male_2"
        
        data = {
            'model': 'speech-01',
            'voice_id': voice_id,
            'speed': '1.0',
            "vol": '1.0',
            "pitch": '0',
            "audio_sample_rate": '32000',
            "bitrate": '128900'  # 必须是32900、64900或128900中的一个
        }
        
        files = {
            'text': open(temp_zip_file, 'rb')
        }
        
        try:
            # 创建异步任务
            self.log_message("提交异步语音合成任务...")
            response = requests.post(url, headers=headers, data=data, files=files)
            response.raise_for_status()
            result = response.json()
            
            # 关闭文件
            files['text'].close()
            
            # 删除临时文件
            os.remove(temp_txt_file)
            os.remove(temp_zip_file)
            
            if "base_resp" not in result or result["base_resp"]["status_code"] != 0:
                error_msg = result.get("base_resp", {}).get("status_msg", "未知错误")
                self.log_message(f"异步API请求失败: {error_msg}", "ERROR")
                return False
            
            # 获取任务ID和文件ID
            task_id = result.get("task_id")
            file_id = result.get("file_id")
            
            if not task_id or not file_id:
                self.log_message("未能获取任务ID或文件ID", "ERROR")
                return False
                
            self.log_message(f"异步任务已创建，任务ID: {task_id}, 文件ID: {file_id}")
            
            # 轮询任务状态
            max_retries = 30  # 最多等待30次，每次10秒
            for i in range(max_retries):
                status = self.check_minimax_async_task(task_id)
                if status == "Success":
                    self.log_message("异步任务处理完成，开始下载结果")
                    # 下载结果
                    return self.download_minimax_async_result(file_id, output_file)
                elif status == "Failed" or status == "Expired":
                    self.log_message(f"异步任务处理失败，状态: {status}", "ERROR")
                    return False
                else:  # Processing
                    self.log_message(f"异步任务处理中，等待中... ({i+1}/{max_retries})")
                    time.sleep(10)  # 等待10秒再次检查
            
            self.log_message("异步任务处理超时", "ERROR")
            return False
            
        except Exception as e:
            self.log_message(f"异步API请求失败: {str(e)}", "ERROR")
            return False
    
    def check_minimax_async_task(self, task_id):
        """检查MiniMax异步任务状态"""
        url = f"https://api.minimax.chat/query/t2a_async_query?GroupId={self.minimax_group_id}&task_id={task_id}"
        headers = {
            "Authorization": f"Bearer {self.minimax_api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            result = response.json()
            
            if "base_resp" not in result or result["base_resp"]["status_code"] != 0:
                error_msg = result.get("base_resp", {}).get("status_msg", "未知错误")
                self.log_message(f"检查任务状态失败: {error_msg}", "ERROR")
                return "Failed"
            
            status = result.get("status")
            return status  # Processing, Success, Failed, Expired
            
        except Exception as e:
            self.log_message(f"检查任务状态失败: {str(e)}", "ERROR")
            return "Failed"
    
    def download_minimax_async_result(self, file_id, output_file):
        """下载MiniMax异步任务结果"""
        try:
            # 获取文件信息
            retrieve_url = f"https://api.minimax.chat/v1/files/retrieve?GroupId={self.minimax_group_id}&file_id={file_id}"
            headers = {
                "Authorization": f"Bearer {self.minimax_api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(retrieve_url, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if "base_resp" not in data or data["base_resp"]["status_code"] != 0:
                error_msg = data.get("base_resp", {}).get("status_msg", "未知错误")
                self.log_message(f"获取文件信息失败: {error_msg}", "ERROR")
                return False
            
            # 下载文件内容
            download_url = f"https://api.minimax.chat/v1/files/retrieve_content?GroupId={self.minimax_group_id}&file_id={file_id}"
            self.log_message(f"开始下载文件，URL: {download_url}")
            
            download_response = requests.get(download_url, headers=headers)
            download_response.raise_for_status()
            
            # 记录响应头信息，用于调试
            self.log_message(f"下载响应头: {dict(download_response.headers)}")
            self.log_message(f"下载内容大小: {len(download_response.content)} 字节")
            
            # 检查内容类型
            content_type = download_response.headers.get('Content-Type', '')
            self.log_message(f"内容类型: {content_type}")
            
            # 如果是音频文件，直接保存
            if 'audio' in content_type or 'mp3' in content_type.lower():
                # 直接保存为音频文件
                with open(output_file, "wb") as f:
                    f.write(download_response.content)
                self.log_message(f"异步API音频文件已直接保存到: {output_file}")
                return True
            
            # 尝试作为ZIP文件处理
            try:
                import zipfile
                from io import BytesIO
                
                zip_content = BytesIO(download_response.content)
                with zipfile.ZipFile(zip_content) as zipf:
                    # 查看ZIP文件内容
                    file_list = zipf.namelist()
                    self.log_message(f"下载的ZIP文件包含以下文件: {file_list}")
                    
                    # 提取音频文件和字幕文件
                    for file_name in file_list:
                        if file_name.endswith('.mp3'):
                            # 保存音频文件
                            with open(output_file, 'wb') as f:
                                f.write(zipf.read(file_name))
                            self.log_message(f"异步API音频文件已保存到: {output_file}")
                        
                        elif file_name.endswith('.json') and ('字幕' in file_name or 'subtitle' in file_name.lower()):
                            # 保存字幕JSON文件
                            json_subtitle_file = output_file.rsplit(".", 1)[0] + ".json"
                            subtitle_data = json.loads(zipf.read(file_name).decode('utf-8'))
                            
                            with open(json_subtitle_file, "w", encoding="utf-8") as f:
                                json.dump(subtitle_data, f, ensure_ascii=False, indent=2)
                            self.log_message(f"异步API字幕JSON文件已保存: {json_subtitle_file}")
                            
                            # 转换为SRT格式并保存
                            srt_subtitle_file = output_file.rsplit(".", 1)[0] + ".srt"
                            self.log_message(f"开始转换为SRT格式，目标文件: {srt_subtitle_file}")
                            
                            if isinstance(subtitle_data, list):
                                success = self.convert_json_to_srt(subtitle_data, srt_subtitle_file)
                                
                                if success:
                                    self.log_message(f"异步API SRT字幕文件已成功保存: {srt_subtitle_file}")
                                    # 优化SRT字幕文件
                                    self.optimize_srt_file(srt_subtitle_file)
                                else:
                                    self.log_message(f"异步API SRT字幕文件转换失败", "ERROR")
                            else:
                                self.log_message(f"异步API字幕数据格式不是列表，无法转换为SRT", "ERROR")
                    return True
            except zipfile.BadZipFile:
                self.log_message("下载的内容不是有效的ZIP文件，尝试其他处理方式")
            
            # 尝试作为JSON处理
            try:
                json_data = json.loads(download_response.content)
                self.log_message(f"内容似乎是JSON数据，尝试处理")
                
                # 保存JSON文件
                json_file = output_file.rsplit('.', 1)[0] + '.json'
                with open(json_file, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, ensure_ascii=False, indent=2)
                self.log_message(f"JSON数据已保存到: {json_file}")
                
                # 如果看起来像字幕数据，尝试转换为SRT
                if isinstance(json_data, list) and len(json_data) > 0:
                    srt_file = output_file.rsplit('.', 1)[0] + '.srt'
                    success = self.convert_json_to_srt(json_data, srt_file)
                    if success:
                        self.log_message(f"SRT字幕文件已成功保存: {srt_file}")
                        # 优化SRT字幕文件
                        self.optimize_srt_file(srt_file)
                    else:
                        self.log_message(f"SRT字幕文件转换失败", "ERROR")
                return True
            except json.JSONDecodeError:
                self.log_message("内容不是JSON数据，直接保存为二进制文件")
            
            # 如果所有尝试都失败，直接保存原始文件
            with open(output_file, 'wb') as f:
                f.write(download_response.content)
            self.log_message(f"原始文件已保存到: {output_file}")
            return True
            
            return True
            
        except Exception as e:
            self.log_message(f"下载异步任务结果失败: {str(e)}", "ERROR")
            return False

    def generate_azure_audio(self, text, output_file, max_retries=3):
        """使用Azure TTS生成音频，支持重试和分块处理"""
        try:
            # 将文本分成较小的块
            text_chunks = self.split_text_into_chunks(text)
            total_chunks = len(text_chunks)
            self.log_message(f"准备处理 {total_chunks} 个文本块...")
            
            # 创建临时文件列表
            temp_files = []
            subtitles = []
            current_index = 1
            total_duration = 0
            
            # 创建进度条
            with tqdm(total=total_chunks, desc="处理文本块", unit="chunk") as chunk_pbar:
                for chunk_idx, chunk in enumerate(text_chunks):
                    self.log_message(f"开始处理第 {chunk_idx + 1} 个文本块，长度: {len(chunk)} 字符")
                    retry_count = 0
                    success = False
                    
                    while not success and retry_count < max_retries:
                        try:
                            # 为每个块创建临时文件
                            temp_file = f"{output_file}.part{chunk_idx}"
                            temp_files.append(temp_file)
                            
                            synthesizer = speechsdk.SpeechSynthesizer(
                                speech_config=self.speech_config,
                                audio_config=speechsdk.audio.AudioOutputConfig(filename=temp_file)
                            )
                            
                            # 存储当前块的字幕信息
                            current_segment = []
                            segment_start_time = None
                            segment_end_time = None
                            first_word_of_segment = True
                            
                            def handle_boundary_event(evt):
                                nonlocal current_index, current_segment, segment_start_time, segment_end_time
                                nonlocal first_word_of_segment, total_duration
                                
                                if evt.text.strip():
                                    # 计算相对于当前块的时间
                                    current_time = evt.audio_offset / 10000000  # 转换为秒
                                    
                                    if first_word_of_segment:
                                        segment_start_time = total_duration + current_time
                                        first_word_of_segment = False
                                    
                                    word_duration = evt.duration.total_seconds()
                                    segment_end_time = total_duration + current_time + word_duration
                                    
                                    word = evt.text.strip()
                                    current_segment.append(word)
                                    
                                    # 在句子结束时添加字幕
                                    if any(word.endswith(p) for p in ('.', ',', '。', '，', '!', '?', '！', '？')):
                                        segment_text = ' '.join(current_segment)
                                        # 移除末尾的标点符号
                                        segment_text = segment_text.rstrip('.,。，!?！？')
                                        
                                        if segment_text:  # 确保有内容再添加字幕
                                            subtitles.append(f"{current_index}\n"
                                                           f"{self.format_timestamp(segment_start_time)} --> {self.format_timestamp(segment_end_time)}\n"
                                                           f"{segment_text}\n")
                                            current_index += 1
                                        
                                        current_segment = []
                                        first_word_of_segment = True
                            
                            # 连接事件处理器
                            synthesizer.synthesis_word_boundary.connect(handle_boundary_event)
                            
                            # 合成语音
                            self.log_message(f"正在合成第 {chunk_idx + 1} 个文本块...")
                            result = synthesizer.speak_text_async(chunk).get()
                            
                            if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
                                self.log_message(f"第 {chunk_idx + 1} 个文本块合成成功")
                                # 更新总时长
                                audio = AudioSegment.from_file(temp_file)
                                chunk_duration = audio.duration_seconds
                                # 添加一个小的间隔
                                total_duration += chunk_duration + 0.1  # 每个块之间添加0.1秒间隔
                                success = True
                            else:
                                raise Exception("语音合成失败")
                                
                        except Exception as e:
                            retry_count += 1
                            self.log_message(f"处理文本块 {chunk_idx + 1}/{total_chunks} 失败: {str(e)}，尝试重试 ({retry_count}/{max_retries})")
                            if retry_count >= max_retries:
                                raise Exception(f"处理文本块失败，已达到最大重试次数: {str(e)}")
                    
                    chunk_pbar.update(1)
            
            # 合并所有临时文件，添加间隔
            self.log_message("开始合并音频文件...")
            combined = AudioSegment.empty()
            silence = AudioSegment.silent(duration=100)  # 100ms的静音
            
            for temp_file in temp_files:
                if combined.duration_seconds > 0:
                    combined += silence  # 在每个块之间添加静音
                audio = AudioSegment.from_file(temp_file)
                combined += audio
            
            # 导出合并后的文件
            self.log_message("保存最终音频文件...")
            combined.export(output_file, format='mp3')
            
            # 保存字幕文件
            srt_file = output_file.rsplit('.', 1)[0] + '.srt'
            with open(srt_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(subtitles))
            
            # 优化SRT字幕文件
            self.optimize_srt_file(srt_file)
            
            # 清理临时文件
            self.log_message("清理临时文件...")
            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                except Exception as e:
                    self.log_message(f"清理临时文件失败: {str(e)}", "WARNING")
            
            return True
        
        except Exception as e:
            self.log_message(f"Azure TTS错误: {str(e)}", "ERROR")
            # 清理临时文件
            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                except Exception as e:
                    self.log_message(f"清理临时文件失败: {str(e)}", "WARNING")
            return False

    def split_text_into_chunks(self, text, max_chars=1000):  
        """将文本分成较小的块，以避免连接超时"""
        chunks = []
        current_chunk = []
        current_length = 0
        
        # 按句子分割文本
        sentences = text.replace('。', '。\n').replace('！', '！\n').replace('？', '？\n').replace('\n\n', '\n').split('\n')
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            sentence_length = len(sentence)
            
            # 如果单个句子超过最大长度，按标点符号分割
            if sentence_length > max_chars:
                sub_sentences = []
                temp = ''
                for char in sentence:
                    temp += char
                    if len(temp) >= max_chars or char in ('，', '；', '、', ',', ';'):
                        if temp:
                            sub_sentences.append(temp)
                            temp = ''
                if temp:
                    sub_sentences.append(temp)
                    
                for sub in sub_sentences:
                    if current_length + len(sub) > max_chars:
                        chunks.append(''.join(current_chunk))
                        current_chunk = [sub]
                        current_length = len(sub)
                    else:
                        current_chunk.append(sub)
                        current_length += len(sub)
            else:
                # 如果当前块加上新句子超过最大长度，创建新块
                if current_length + sentence_length > max_chars:
                    chunks.append(''.join(current_chunk))
                    current_chunk = [sentence]
                    current_length = sentence_length
                else:
                    current_chunk.append(sentence)
                    current_length += sentence_length
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(''.join(current_chunk))
        
        return chunks

    def merge_audio_files(self, audio_files, output_file):
        """合并多个音频文件"""
        try:
            from pydub import AudioSegment
            
            # 读取第一个文件作为基础
            combined = AudioSegment.from_mp3(audio_files[0])
            
            # 添加其他文件
            for audio_file in audio_files[1:]:
                audio = AudioSegment.from_mp3(audio_file)
                combined += audio
            
            # 导出合并后的文件
            combined.export(output_file, format='mp3')
            return True
        except Exception as e:
            self.log_message(f"合并音频文件失败: {str(e)}", "ERROR")
            return False

    def process_single_file(self, file_name, whisper_model="small"):
        """处理单个文件
        
        Args:
            file_name: 要处理的文件名
            whisper_model: Whisper模型大小，可选值: "tiny", "base", "small", "medium", "large"
        """
        try:
            self.log_message(f"\n开始处理文件: {file_name}")
            
            # 获取文件内容
            content = self.read_docx(file_name)
            if content is None:
                self.log_message(f"文件处理失败: {file_name}", "ERROR")
                return False
            
            # 创建输出文件夹
            output_dir = os.path.join(self.desktop_path, "Youtube/输出语音")
            os.makedirs(output_dir, exist_ok=True)
            
            # 设置输出文件路径
            output_file = os.path.join(output_dir, f"{file_name}.mp3")
            
            # 根据选择的模式生成音频
            mode = self.file_modes.get(file_name, "normal")
            
            if mode == "azure":
                success = self.generate_azure_audio(content, output_file)
            else:
                success = self.call_minimax_api(content, output_file, mode, generate_srt=False)  # 不生成SRT
            
            if success:
                self.log_message(f"文件处理成功: {file_name}")
                
                # 直接使用Whisper生成字幕
                self.log_message(f"正在使用Whisper({whisper_model}模型)进行语音识别和字幕生成...")
                whisper_srt_file = self.generate_whisper_subtitles(output_file, model_size=whisper_model)
                
                if whisper_srt_file and os.path.exists(whisper_srt_file):
                    # 使用原始Word文档内容校正Whisper识别的字幕
                    self.log_message("正在使用原始Word文档内容校正Whisper识别的字幕...")
                    corrected_srt_file = output_file.rsplit('.', 1)[0] + '.srt'  # 最终SRT文件
                    self.correct_whisper_with_word_content(whisper_srt_file, content, corrected_srt_file)
                    self.log_message(f"生成校正后的字幕文件: {corrected_srt_file}")
                    
                    # 清理中间文件
                    try:
                        if os.path.exists(whisper_srt_file):
                            os.remove(whisper_srt_file)
                            self.log_message(f"已清理中间文件: {whisper_srt_file}")
                    except Exception as e:
                        self.log_message(f"清理中间文件失败: {str(e)}", "WARNING")
                
                return True
            else:
                self.log_message(f"文件处理失败: {file_name}", "ERROR")
                return False
                
        except Exception as e:
            self.log_message(f"处理文件时出错: {str(e)}", "ERROR")
            return False

    def process_files(self):
        """处理所有选中的文件"""
        self.log_message("\n开始处理选中的文件...")
        
        # 获取所有需要处理的文件
        files_to_process = [f for f in self.file_modes.keys()]
        total_files = len(files_to_process)
        
        # 使用tqdm创建总体进度条
        with tqdm(total=total_files, desc="总体进度", unit="file") as pbar:
            for i, file_name in enumerate(files_to_process, 1):
                self.log_message(f"\n处理文件 ({i}/{total_files}): {file_name}")
                success = self.process_single_file(file_name, whisper_model=self.whisper_model)
                pbar.update(1)
                if not success:
                    self.log_message(f"文件处理失败: {file_name}", "ERROR")
        
        self.log_message("\n所有文件处理完成!")

    def get_docx_files(self):
        """获取改写文件夹中的docx文件"""
        try:
            if not os.path.exists(self.rewrite_path):
                self.log_message("改写文件夹不存在", "ERROR")
                return []
            
            # 获取所有文件
            files = [f for f in os.listdir(self.rewrite_path) 
                    if os.path.isfile(os.path.join(self.rewrite_path, f)) and 
                    f.endswith(('.docx', '.txt')) and 
                    not f.startswith('~$')]  # 排除临时文件
            
            # 存储文件名和原始文件名的映射
            self.file_mapping = {}
            display_files = []
            
            for file in sorted(files):
                # 去掉扩展名用于显示
                display_name = os.path.splitext(file)[0]
                if display_name not in self.file_mapping:
                    display_files.append(display_name)
                    self.file_mapping[display_name] = file
            
            self.log_message(f"改写文件夹路径: {self.rewrite_path}")
            self.log_message(f"改写文件夹文件总数: {len(files)}")
            
            return display_files
            
        except Exception as e:
            self.log_message(f"获取文件列表失败: {str(e)}", "ERROR")
            return []

    def combine_whisper_timestamps_with_original_text(self, whisper_srt_file, original_srt_file, output_srt_file=None):
        """结合Whisper的时间戳和原始字幕的文本内容，使用GPT-4o-mini修正文本
        
        保持Whisper的时间戳不变，使用GPT-4o-mini对比原始文本和Whisper文本，修正错别字
        
        Args:
            whisper_srt_file: Whisper生成的SRT文件路径（包含准确的时间戳）
            original_srt_file: 原始SRT文件路径（包含准确的文本内容）
            output_srt_file: 输出SRT文件路径，如果为None，则默认为original_srt_file + '_perfect.srt'
        """
        try:
            self.log_message(f"开始使用GPT-4o-mini修正Whisper字幕文本...")
            
            # 读取Whisper生成的SRT文件
            with open(whisper_srt_file, 'r', encoding='utf-8') as f:
                whisper_content = f.read()
            
            # 读取原始SRT文件
            with open(original_srt_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # 解析SRT内容
            whisper_blocks = whisper_content.strip().split('\n\n')
            original_blocks = original_content.strip().split('\n\n')
            
            # 提取Whisper字幕的时间戳和文本
            whisper_segments = []
            for block in whisper_blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue
                
                # 提取索引、时间轴和文本
                index = lines[0]
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                whisper_segments.append({
                    'index': index,
                    'time_line': time_line,
                    'text': text
                })
            
            # 提取原始字幕的文本内容
            original_text = ""
            for block in original_blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue
                
                # 提取文本
                text = '\n'.join(lines[2:])
                original_text += text + " "
            
            # 准备GPT-4o-mini的输入
            whisper_texts = [segment['text'] for segment in whisper_segments]
            
            # 调用GPT-4o-mini进行文本修正
            corrected_texts = self.correct_texts_with_gpt(whisper_texts, original_text)
            
            # 创建新的字幕块
            new_blocks = []
            for i, segment in enumerate(whisper_segments):
                if i < len(corrected_texts):
                    # 使用修正后的文本
                    corrected_text = corrected_texts[i]
                    new_blocks.append(f"{segment['index']}\n{segment['time_line']}\n{corrected_text}")
                else:
                    # 如果没有对应的修正文本，使用原始Whisper文本
                    new_blocks.append(f"{segment['index']}\n{segment['time_line']}\n{segment['text']}")
            
            # 设置输出文件路径
            if output_srt_file is None:
                output_srt_file = original_srt_file.rsplit('.', 1)[0] + '_perfect.srt'
            
            # 写入结合后的SRT文件
            with open(output_srt_file, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(new_blocks))
            
            self.log_message(f"GPT-4o-mini修正完成，保存到: {output_srt_file}")
            return True
            
        except Exception as e:
            self.log_message(f"使用GPT-4o-mini修正字幕失败: {str(e)}", "ERROR")
            return False

    def correct_texts_with_gpt(self, whisper_texts, original_text):
        """使用GPT-4o-mini修正Whisper文本中的错别字，并确保句子边界正确
        
        Args:
            whisper_texts: Whisper识别的文本列表
            original_text: 原始文本内容
            
        Returns:
            修正后的文本列表
        """
        try:
            from openai import OpenAI
            
            self.log_message("正在使用GPT-4o-mini修正文本和句子边界...")
            
            # 初始化OpenAI客户端
            client = OpenAI(
                base_url="",
                api_key=""
            )
            
            # 构建提示词，强调句子边界的重要性
            prompt = f"""
你是一个专业的字幕修正助手。我需要你帮助修正语音识别生成的字幕文本中可能存在的错别字，并确保句子边界正确。

原始准确文本（来自Word文档，绝对正确）:
"{original_text}"

Whisper识别的文本（可能包含错别字和句子边界问题）:
"""
            
            # 添加Whisper文本，并标明每句的编号
            for i, text in enumerate(whisper_texts):
                prompt += f"\n[{i+1}] {text}"
            
            prompt += """

请执行以下任务：
1. 逐句修正Whisper识别的文本，使其与原始准确文本的意思一致
2. 确保每个句子的边界正确，避免出现"串行"问题（即一句话的内容错误地分到下一句）
3. 保持Whisper的断句数量，但修正每句的内容，使其成为完整且独立的句子
4. 如果发现某句话内容应该属于另一句，请适当调整

你的输出应该只包含修正后的文本，每句一行，使用与输入相同的编号格式：
[1] 修正后的第一句
[2] 修正后的第二句
...

保持原始的标点符号和格式，只修正错别字和句子边界问题。
"""
            
            # 调用GPT-4o-mini
            response = client.chat.completions.create(
                model="gpt-4o-mini-2024-07-18",
                messages=[
                    {"role": "system", "content": "你是一个专业的字幕修正助手，擅长修正语音识别文本中的错别字和句子边界问题。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,  # 使用较低的温度以获得更确定的结果
                max_tokens=4000
            )
            
            # 获取响应文本
            corrected_text = response.choices[0].message.content.strip()
            
            # 分割为单独的行
            corrected_lines = corrected_text.split('\n')
            
            # 过滤掉空行和可能的额外信息
            corrected_lines = [line.strip() for line in corrected_lines if line.strip()]
            
            # 提取编号和文本
            corrected_texts = []
            for line in corrected_lines:
                # 匹配 [数字] 文本 格式
                match = re.match(r'^\[(\d+)\]\s*(.*)', line)
                if match:
                    index = int(match.group(1)) - 1
                    text = match.group(2).strip()
                    
                    # 确保索引在范围内
                    while len(corrected_texts) <= index:
                        corrected_texts.append("")
                    
                    corrected_texts[index] = text
                else:
                    # 如果没有编号格式，直接添加到结果中
                    corrected_texts.append(line)
            
            self.log_message(f"GPT-4o-mini成功修正了 {len(corrected_texts)} 行文本，并确保句子边界正确")
            
            return corrected_texts
            
        except Exception as e:
            self.log_message(f"GPT-4o-mini修正文本失败: {str(e)}", "ERROR")
            return whisper_texts  # 如果失败，返回原始文本

    def correct_whisper_with_word_content(self, whisper_srt_file, original_content, output_srt_file=None):
        """使用Word文档原始内容校正Whisper识别的字幕
        
        保持Whisper的时间戳不变，使用GPT-4o-mini对比原始文本和Whisper文本，修正错别字和句子边界问题
        
        Args:
            whisper_srt_file: Whisper生成的SRT文件路径（包含准确的时间戳）
            original_content: Word文档的原始内容（包含准确的文本）
            output_srt_file: 输出SRT文件路径，如果为None，则默认为whisper_srt_file + '_corrected.srt'
        """
        try:
            self.log_message(f"开始使用GPT-4o-mini校正Whisper字幕文本和句子边界...")
            
            # 读取Whisper生成的SRT文件
            with open(whisper_srt_file, 'r', encoding='utf-8') as f:
                whisper_content = f.read()
            
            # 解析SRT内容
            whisper_blocks = whisper_content.strip().split('\n\n')
            
            # 提取Whisper字幕的时间戳和文本
            whisper_segments = []
            for block in whisper_blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue
                
                # 提取索引、时间轴和文本
                index = lines[0]
                time_line = lines[1]
                text = '\n'.join(lines[2:])
                
                whisper_segments.append({
                    'index': index,
                    'time_line': time_line,
                    'text': text
                })
            
            # 提取原始Word文档的文本内容
            original_text = original_content.strip()
            
            # 准备GPT-4o-mini的输入
            whisper_texts = [segment['text'] for segment in whisper_segments]
            
            # 调用GPT-4o-mini进行文本修正和句子边界校正
            corrected_texts = self.correct_texts_with_gpt(whisper_texts, original_text)
            
            # 创建新的字幕块
            new_blocks = []
            for i, segment in enumerate(whisper_segments):
                if i < len(corrected_texts) and corrected_texts[i]:
                    # 使用修正后的文本
                    corrected_text = corrected_texts[i]
                    new_blocks.append(f"{segment['index']}\n{segment['time_line']}\n{corrected_text}")
                else:
                    # 如果没有对应的修正文本，使用原始Whisper文本
                    new_blocks.append(f"{segment['index']}\n{segment['time_line']}\n{segment['text']}")
            
            # 设置输出文件路径
            if output_srt_file is None:
                output_srt_file = whisper_srt_file.rsplit('.', 1)[0] + '_corrected.srt'
            
            # 写入校正后的SRT文件
            with open(output_srt_file, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(new_blocks))
            
            self.log_message(f"GPT-4o-mini校正完成，保存到: {output_srt_file}")
            return True
            
        except Exception as e:
            self.log_message(f"使用GPT-4o-mini校正字幕失败: {str(e)}", "ERROR")
            return False

    def align_subtitles_with_audio(self, audio_file, srt_file):
        """使用语音识别技术将字幕与音频精确对齐"""
        try:
            self.log_message(f"开始将字幕与音频对齐: {srt_file}")
            
            # 导入必要的库
            import speech_recognition as sr
            from pydub import AudioSegment
            import jieba
            
            # 读取原始SRT文件
            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析SRT内容，获取所有文本
            srt_blocks = content.strip().split('\n\n')
            all_text = ""
            
            for block in srt_blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue
                
                # 提取文本
                text = '\n'.join(lines[2:])
                all_text += text + " "
            
            # 使用jieba分词
            words = list(jieba.cut(all_text))
            
            # 加载音频文件
            audio = AudioSegment.from_file(audio_file)
            
            # 将音频转换为WAV格式（语音识别需要）
            temp_wav = audio_file.rsplit('.', 1)[0] + '_temp.wav'
            audio.export(temp_wav, format="wav")
            
            # 初始化语音识别器
            r = sr.Recognizer()
            
            # 创建新的字幕块
            new_blocks = []
            new_index = 1
            
            # 分析音频
            with sr.AudioFile(temp_wav) as source:
                # 调整识别器参数以适应音频
                r.adjust_for_ambient_noise(source)
                
                # 设置音频段的长度（秒）
                segment_length = 5  # 5秒一段
                total_duration = len(audio) / 1000  # 总时长（秒）
                
                for i in range(0, int(total_duration), segment_length):
                    # 设置当前段的开始和结束时间
                    start_time = i
                    end_time = min(i + segment_length, total_duration)
                    
                    # 获取当前音频段
                    audio_segment = r.record(source, duration=end_time-start_time)
                    
                    try:
                        # 使用Google语音识别API（需要联网）
                        segment_text = r.recognize_google(audio_segment, language="zh-CN")
                        
                        # 查找原始字幕中最匹配的文本
                        best_match = self.find_best_match(segment_text, all_text)
                        
                        if best_match:
                            # 创建新的字幕块
                            start_time_str = self.format_timestamp(start_time)
                            end_time_str = self.format_timestamp(end_time)
                            
                            # 对匹配的文本进行分割，确保每行不超过13个字符
                            split_texts = self.split_subtitle_text(best_match)
                            
                            # 计算每个分割文本的时长
                            sub_duration = (end_time - start_time) * 1000 / len(split_texts)
                            
                            for j, sub_text in enumerate(split_texts):
                                sub_start = start_time * 1000 + j * sub_duration
                                sub_end = sub_start + sub_duration
                                
                                sub_start_str = self.ms_to_srt_time(sub_start)
                                sub_end_str = self.ms_to_srt_time(sub_end)
                                
                                new_blocks.append(f"{new_index}\n{sub_start_str} --> {sub_end_str}\n{sub_text}")
                                new_index += 1
                    
                    except sr.UnknownValueError:
                        self.log_message(f"无法识别音频段 {start_time}-{end_time} 秒", "WARNING")
                    except sr.RequestError as e:
                        self.log_message(f"无法请求Google语音识别服务: {e}", "ERROR")
                        break
            
            # 删除临时WAV文件
            try:
                os.remove(temp_wav)
            except:
                pass
            
            # 如果没有生成新的字幕块，返回失败
            if not new_blocks:
                self.log_message("未能生成任何对齐的字幕", "ERROR")
                return False
            
            # 写入对齐后的SRT文件
            aligned_srt_file = srt_file.rsplit('.', 1)[0] + '_aligned.srt'
            with open(aligned_srt_file, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(new_blocks))
            
            self.log_message(f"字幕与音频对齐完成，保存到: {aligned_srt_file}")
            return True
            
        except Exception as e:
            self.log_message(f"字幕与音频对齐失败: {str(e)}", "ERROR")
            return False
    
    def align_subtitles_with_whisper(self, audio_file, srt_file):
        """使用Whisper将字幕与音频精确对齐"""
        try:
            self.log_message(f"开始使用Whisper将字幕与音频对齐: {srt_file}")
            
            # 导入必要的库
            import whisper
            import numpy as np
            import tempfile
            from pydub import AudioSegment
            
            # 读取原始SRT文件
            with open(srt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析SRT内容，获取所有文本
            srt_blocks = content.strip().split('\n\n')
            all_text = ""
            
            for block in srt_blocks:
                lines = block.strip().split('\n')
                if len(lines) < 3:
                    continue
                
                # 提取文本
                text = '\n'.join(lines[2:])
                all_text += text + " "

            # 加载Whisper模型（使用较小的模型以提高速度）
            self.log_message("正在加载Whisper模型...")
            model = whisper.load_model("tiny")  # 可选: "tiny", "base", "small", "medium", "large"

            # 将音频转换为WAV格式（如果不是WAV）
            if not audio_file.lower().endswith('.wav'):
                self.log_message("转换音频为WAV格式...")
                audio = AudioSegment.from_file(audio_file)
                temp_wav = audio_file.rsplit('.', 1)[0] + '_temp.wav'
                audio.export(temp_wav, format="wav")
                audio_path = temp_wav
            else:
                audio_path = audio_file

            # 使用Whisper进行语音识别
            self.log_message("开始进行语音识别...")
            result = model.transcribe(audio_path, language="zh")

            # 创建新的字幕块
            new_blocks = []

            # 处理识别结果中的每个段落
            for i, segment in enumerate(result["segments"]):
                start_time = segment["start"]
                end_time = segment["end"]
                text = segment["text"].strip()

                # 如果文本为空，跳过
                if not text:
                    continue

                # 将时间转换为SRT格式
                start_time_str = self.ms_to_srt_time(start_time * 1000)
                end_time_str = self.ms_to_srt_time(end_time * 1000)

                # 对文本进行分割，确保每行不超过13个字符
                split_texts = self.split_subtitle_text(text)

                # 计算每个分割文本的时长
                segment_duration = (end_time - start_time) * 1000
                sub_duration = segment_duration / len(split_texts)

                # 为每个分割文本创建字幕块
                for j, sub_text in enumerate(split_texts):
                    sub_start = start_time * 1000 + j * sub_duration
                    sub_end = sub_start + sub_duration

                    sub_start_str = self.ms_to_srt_time(sub_start)
                    sub_end_str = self.ms_to_srt_time(sub_end)

                    new_blocks.append(f"{len(new_blocks) + 1}\n{sub_start_str} --> {sub_end_str}\n{sub_text}")

            # 删除临时WAV文件
            if not audio_file.lower().endswith('.wav') and os.path.exists(temp_wav):
                try:
                    os.remove(temp_wav)
                except:
                    pass

            # 如果没有生成新的字幕块，返回失败
            if not new_blocks:
                self.log_message("未能生成任何对齐的字幕", "ERROR")
                return False

            # 写入对齐后的SRT文件
            aligned_srt_file = srt_file.rsplit('.', 1)[0] + '_whisper.srt'
            with open(aligned_srt_file, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(new_blocks))

            self.log_message(f"字幕与音频对齐完成，保存到: {aligned_srt_file}")
            return aligned_srt_file

        except Exception as e:
            self.log_message(f"使用Whisper对齐字幕失败: {str(e)}", "ERROR")
            return False

    def find_best_match(self, recognized_text, original_text):
        """查找原始文本中与识别文本最匹配的部分"""
        import difflib

        # 使用difflib查找最佳匹配
        matcher = difflib.SequenceMatcher(None, recognized_text, original_text)
        match = matcher.find_longest_match(0, len(recognized_text), 0, len(original_text))

        if match.size > 5:  # 至少匹配5个字符
            return original_text[match.b:match.b + match.size]

        return None

    def generate_whisper_subtitles(self, audio_file, model_size="small"):
        """使用Whisper直接从音频生成字幕文件

        Args:
            audio_file: 音频文件路径
            model_size: Whisper模型大小，可选值: "tiny", "base", "small", "medium", "large"
                        默认为"small"，平衡了速度和准确度

        Returns:
            生成的SRT文件路径，如果失败则返回False
        """
        try:
            self.log_message(f"开始使用Whisper({model_size}模型)从音频生成字幕: {audio_file}")

            # 导入必要的库
            import whisper
            import numpy as np
            import tempfile
            from pydub import AudioSegment

            # 验证模型大小参数
            valid_models = ["tiny", "base", "small", "medium", "large"]
            if model_size not in valid_models:
                self.log_message(f"无效的模型大小: {model_size}，使用默认的'small'模型", "WARNING")
                model_size = "small"

            # 加载Whisper模型
            self.log_message(f"正在加载Whisper {model_size}模型...")
            model = whisper.load_model(model_size)

            # 将音频转换为WAV格式（如果不是WAV）
            if not audio_file.lower().endswith('.wav'):
                self.log_message("转换音频为WAV格式...")
                audio = AudioSegment.from_file(audio_file)
                temp_wav = audio_file.rsplit('.', 1)[0] + '_temp.wav'
                audio.export(temp_wav, format="wav")
                audio_path = temp_wav
            else:
                audio_path = audio_file

            # 使用Whisper进行语音识别，设置更多参数以提高准确性
            self.log_message("开始进行语音识别...")
            result = model.transcribe(
                audio_path,
                language="zh",
                word_timestamps=True,  # 获取更精确的单词时间戳
                condition_on_previous_text=True,  # 考虑前文上下文
                fp16=False  # 使用更精确的浮点计算
            )

            # 创建新的字幕块
            new_blocks = []

            # 处理识别结果中的每个段落
            for i, segment in enumerate(result["segments"]):
                start_time = segment["start"]
                end_time = segment["end"]
                text = segment["text"].strip()

                # 如果文本为空，跳过
                if not text:
                    continue

                # 将时间转换为SRT格式
                start_time_str = self.ms_to_srt_time(start_time * 1000)
                end_time_str = self.ms_to_srt_time(end_time * 1000)

                # 对文本进行分割，确保每行不超过13个字符
                split_texts = self.split_subtitle_text(text)

                # 计算每个分割文本的时长
                segment_duration = (end_time - start_time) * 1000
                sub_duration = segment_duration / len(split_texts)

                # 为每个分割文本创建字幕块
                for j, sub_text in enumerate(split_texts):
                    sub_start = start_time * 1000 + j * sub_duration
                    sub_end = sub_start + sub_duration

                    sub_start_str = self.ms_to_srt_time(sub_start)
                    sub_end_str = self.ms_to_srt_time(sub_end)

                    new_blocks.append(f"{len(new_blocks) + 1}\n{sub_start_str} --> {sub_end_str}\n{sub_text}")

            # 删除临时WAV文件
            if not audio_file.lower().endswith('.wav') and os.path.exists(temp_wav):
                try:
                    os.remove(temp_wav)
                except:
                    pass

            # 如果没有生成新的字幕块，返回失败
            if not new_blocks:
                self.log_message("未能生成任何字幕", "ERROR")
                return False

            # 写入生成的SRT文件
            whisper_srt_file = audio_file.rsplit('.', 1)[0] + '_whisper_temp.srt'
            with open(whisper_srt_file, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(new_blocks))

            self.log_message(f"Whisper字幕生成完成，保存到: {whisper_srt_file}")
            return whisper_srt_file

        except Exception as e:
            self.log_message(f"使用Whisper生成字幕失败: {str(e)}", "ERROR")
            return False


def main():
    generator = TTSGenerator()

if __name__ == "__main__":
    main()